<div class="min-h-screen bg-stone-50">
  <div class="w-full py-8 mx-auto max-w-7xl">

    <%# Back Button %>
    <div class="mb-6">
      <%= link_to scout_jobs_path, class: "inline-flex items-center px-4 py-2 text-sm font-medium text-stone-700 bg-white border border-stone-300 rounded-md shadow-sm hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500" do %>
        <%= phosphor_icon "arrow-left", class: "w-4 h-4 mr-2" %>
        Back to Jobs
      <% end %>
    </div>

    <%# Single Comprehensive Card - Job Details %>
    <div class="mb-6 overflow-hidden bg-white border rounded-lg shadow-md border-stone-200">
      <div class="p-8">
        <%# Job Header Section %>
        <div class="mb-10">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-start gap-4">
                <%# Job Title and Description %>
                <div class="flex-1 min-w-0">
                  <h1 class="text-2xl font-semibold text-stone-900"><%= @job.title %></h1>
                  <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Job posting details</p>
                </div>
              </div>
            </div>

            <%# Action Buttons in top right %>
            <div class="flex flex-col space-y-2">
              <%# Payment Buttons based on status %>
              <% if @job.draft? || @job.expired? %>
                <%= button_to "Publish Job",
                      scout_job_payment_path(@job),
                      method: :post,
                      params: { plan: 'price_1R9Q75DYYVPVcCCrJyRB0SP1' }, # Basic Job Price ID
                      class: "inline-flex items-center justify-center px-6 py-2.5 text-sm font-medium text-white bg-stone-900 rounded-md shadow-sm hover:bg-stone-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-colors",
                      data: { turbo: false } %>
              <% elsif @job.published? && !@job.is_premium? %>
                <%= button_to "Upgrade to Premium",
                      scout_job_payment_path(@job),
                      method: :post,
                      params: { plan: 'price_1R9Q7QDYYVPVcCCrcK2WYqcV', type: "premium_upgrade" }, # Premium Job Price ID
                      class: "inline-flex items-center justify-center px-6 py-2.5 text-sm font-medium text-white bg-amber-600 rounded-md shadow-sm hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transition-colors",
                      data: { turbo: false } %>
              <% end %>

              <%# Edit Button %>
              <%= link_to "Edit Job", edit_scout_job_path(@job), class: "inline-flex items-center justify-center px-6 py-2.5 text-sm font-medium text-stone-700 bg-white border border-stone-300 rounded-md shadow-sm hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 transition-colors" %>
            </div>
          </div>
        </div>

        <%# Enhanced Separator %>
        <div class="py-2 mt-3 border-t border-stone-200"></div>

        <%# Job Overview Section %>
        <div class="mb-12">
          <div class="mb-8">
            <h3 class="text-lg font-semibold leading-7 text-stone-900">Job Overview</h3>
            <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Basic job information and status</p>
          </div>

          <dl class="space-y-6">
            <%# Status Display %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Status</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <% status_class = case @job.status.to_sym
                                  when :published
                                    'text-green-800 bg-green-100'
                                  when :draft
                                    'text-yellow-800 bg-yellow-100'
                                  when :expired
                                    'text-red-800 bg-red-100'
                                  else
                                    'text-stone-800 bg-stone-100'
                                  end %>
                <span class="px-2 py-1 text-xs font-medium rounded-full <%= status_class %>">
                  <%= @job.status.humanize %>
                </span>
              </dd>
            </div>

            <%# Listing Type %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Listing Type</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <% if @job.is_premium? %>
                  <span class="inline-flex items-center px-3 py-1 text-sm font-medium border rounded-md text-purple-700 bg-purple-50 border-purple-200">
                    <%= phosphor_icon "star", class: "w-4 h-4 mr-1.5" %>
                    Premium Listing
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-3 py-1 text-sm font-medium border rounded-md text-stone-700 bg-stone-100 border-stone-200">
                    Standard Listing
                  </span>
                <% end %>
              </dd>
            </div>

            <%# Published Date %>
            <% if @job.published_at.present? %>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm font-medium leading-6 text-stone-900">Published On</dt>
                <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                  <%= @job.published_at.strftime("%B %d, %Y") %>
                </dd>
              </div>
            <% end %>

            <%# Expiry Date %>
            <% if @job.expires_at.present? %>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm font-medium leading-6 text-stone-900">Expires On</dt>
                <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                  <%= @job.expires_at.strftime("%B %d, %Y") %>
                </dd>
              </div>
            <% end %>

            <%# Job Category %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Job Category</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.job_category.present? ? @job.job_category.to_s.humanize : 'Not specified' %>
              </dd>
            </div>

            <%# Budget Range %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Budget Range</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.salary_range %>
              </dd>
            </div>

            <%# Payment Frequency %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Payment Frequency</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.payment_frequency.present? ? @job.payment_frequency.to_s.humanize : 'Not specified' %>
              </dd>
            </div>

            <%# Application Deadline %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Application Deadline</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.application_deadline %>
              </dd>
            </div>
          </dl>
        </div>

        <%# Enhanced Separator %>
        <div class="py-2 mt-3 border-t border-stone-200"></div>

        <%# Job Details Section %>
        <div class="mb-12">
          <div class="mb-8">
            <h3 class="text-lg font-semibold leading-7 text-stone-900">Job Details</h3>
            <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Detailed job specifications and requirements</p>
          </div>

          <dl class="space-y-6">
            <%# Description %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Description</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= simple_format(@job.description) if @job.description.present? %>
              </dd>
            </div>

            <%# Requirements %>
            <% if @job.requirements.present? %>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm font-medium leading-6 text-stone-900">Requirements</dt>
                <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                  <%= simple_format(@job.requirements) %>
                </dd>
              </div>
            <% end %>

            <%# Work Duration %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Work Duration</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.work_duration.present? ? @job.work_duration.humanize : 'Not specified' %>
              </dd>
            </div>

            <%# Desired Outcome %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Desired Outcome</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.outcome.present? ? @job.outcome.to_s.humanize : 'Not specified' %>
              </dd>
            </div>

            <%# Involvement Level %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Involvement Level</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.involvement_level.present? ? @job.involvement_level.humanize : 'Not specified' %>
              </dd>
            </div>

            <%# Topics %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Topics</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <% if @job.topics.present? && @job.topics.any? %>
                  <div class="flex flex-wrap gap-2">
                    <% @job.topics.each do |topic| %>
                      <span class="inline-flex items-center px-3 py-1 text-sm font-medium border rounded-md text-stone-700 bg-stone-100 border-stone-200">
                        <%= phosphor_icon "tag", class: "h-4 w-4 mr-1.5" %>
                        <%= topic %>
                      </span>
                    <% end %>
                  </div>
                <% else %>
                  <span class="text-stone-500">Not specified</span>
                <% end %>
              </dd>
            </div>
          </dl>
        </div>

        <%# Enhanced Separator %>
        <div class="py-2 mt-3 border-t border-stone-200"></div>

        <%# Category-Specific Details Section %>
        <div class="mb-12">
          <div class="mb-8">
            <h3 class="text-lg font-semibold leading-7 text-stone-900">Category-Specific Details</h3>
            <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Details specific to the <%= @job.job_category.present? ? @job.job_category.humanize.downcase : 'selected' %> category</p>
          </div>

          <dl class="space-y-6">
            <% if @job.social_media? %>
              <%# Social Media Specific Fields %>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm font-medium leading-6 text-stone-900">Platform</dt>
                <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                  <%= @job.platform.present? ? @job.platform.to_s.humanize : 'Not specified' %>
                </dd>
              </div>

              <% if @job.leads? || @job.booked_calls? %>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
                  <dt class="text-sm font-medium leading-6 text-stone-900">Social Media Goal Type</dt>
                  <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                    <%= @job.social_media_goal_type.present? ? @job.social_media_goal_type.humanize : 'Not specified' %>
                  </dd>
                </div>

                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
                  <dt class="text-sm font-medium leading-6 text-stone-900">Risk Acknowledgment</dt>
                  <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                    <% if @job.social_media_understands_risk_acknowledged %>
                      <span class="inline-flex items-center px-3 py-1 text-sm font-medium border rounded-md text-green-700 bg-green-100 border-green-200">
                        <%= phosphor_icon "check", class: "w-4 h-4 mr-1.5" %>
                        Acknowledged
                      </span>
                    <% else %>
                      <span class="text-stone-500">Not acknowledged</span>
                    <% end %>
                  </dd>
                </div>
              <% end %>

            <% elsif @job.newsletter? %>
              <%# Newsletter Specific Fields %>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm font-medium leading-6 text-stone-900">Newsletter Frequency</dt>
                <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                  <%= @job.newsletter_frequency.present? ? @job.newsletter_frequency.humanize : 'Not specified' %>
                </dd>
              </div>

              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm font-medium leading-6 text-stone-900">Newsletter Length</dt>
                <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                  <%= @job.newsletter_length.present? ? @job.newsletter_length.humanize : 'Not specified' %>
                </dd>
              </div>

            <% elsif @job.lead_magnet? %>
              <%# Lead Magnet Specific Fields %>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm font-medium leading-6 text-stone-900">Lead Magnet Type</dt>
                <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                  <%= @job.lead_magnet_type.present? ? @job.lead_magnet_type.humanize : 'Not specified' %>
                </dd>
              </div>

            <% else %>
              <%# Fallback for unknown or missing category %>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm font-medium leading-6 text-stone-900">Category Details</dt>
                <dd class="mt-2 text-sm leading-6 text-stone-500 sm:col-span-2 sm:mt-0">
                  No category-specific details available
                </dd>
              </div>
            <% end %>
          </dl>
        </div>

        <%# Enhanced Separator %>
        <div class="py-2 mt-3 border-t border-stone-200"></div>

        <%# Client Information Section %>
        <div>
          <div class="mb-8">
            <h3 class="text-lg font-semibold leading-7 text-stone-900">Client Information</h3>
            <p class="max-w-2xl mt-1 text-sm leading-6 text-stone-500">Background information about the client and business</p>
          </div>

          <dl class="space-y-6">
            <%# Current Client Count %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Current Client Count</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.client_count.present? ? @job.client_count : 'Not specified' %>
              </dd>
            </div>

            <%# Average Charge Per Client %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Average Charge Per Client</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= @job.charge_per_client.present? ? @job.charge_per_client : 'Not specified' %>
              </dd>
            </div>

            <%# Current Business Challenge %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Current Business Challenge</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= simple_format(@job.business_challenge) if @job.business_challenge.present? %>
                <% unless @job.business_challenge.present? %>
                  <span class="text-stone-500">Not specified</span>
                <% end %>
              </dd>
            </div>

            <%# Current Offer & Target Audience %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Current Offer & Target Audience</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= simple_format(@job.offer_summary) if @job.offer_summary.present? %>
                <% unless @job.offer_summary.present? %>
                  <span class="text-stone-500">Not specified</span>
                <% end %>
              </dd>
            </div>

            <%# Target Audience %>
            <% if @job.target_audience_description.present? %>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm font-medium leading-6 text-stone-900">Target Audience</dt>
                <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                  <%= simple_format(@job.target_audience_description) %>
                </dd>
              </div>
            <% end %>

            <%# Brands to Emulate %>
            <% if @job.emulated_brands_description.present? %>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm font-medium leading-6 text-stone-900">Brands to Emulate</dt>
                <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                  <%= simple_format(@job.emulated_brands_description) %>
                </dd>
              </div>
            <% end %>

            <%# Additional Information %>
            <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-6">
              <dt class="text-sm font-medium leading-6 text-stone-900">Additional Information</dt>
              <dd class="mt-2 text-sm leading-6 text-stone-700 sm:col-span-2 sm:mt-0">
                <%= simple_format(@job.useful_info) if @job.useful_info.present? %>
                <% unless @job.useful_info.present? %>
                  <span class="text-stone-500">Not specified</span>
                <% end %>
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>

  </div>
</div>
